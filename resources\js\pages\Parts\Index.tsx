import { Head, <PERSON>, router } from '@inertiajs/react';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { 
    Search, 
    Filter, 
    Grid3X3, 
    List,
    Heart,
    MapPin,
    Calendar,
    Eye
} from 'lucide-react';
import { ProductImage } from '@/components/ui/responsive-image';

interface Part {
    id: number;
    name: string;
    description: string;
    price: number;
    condition: 'new' | 'used' | 'refurbished';
    status: 'active' | 'inactive' | 'sold';
    slug: string;
    views: number;
    created_at: string;
    images: any[];
    user: {
        id: number;
        name: string;
        location?: string;
    };
    category: {
        id: number;
        name: string;
        slug: string;
    };
    compatible_vehicles?: string[];
}

interface PaginatedParts {
    data: Part[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

interface Category {
    id: number;
    name: string;
    slug: string;
}

interface Props {
    parts: PaginatedParts;
    categories: Category[];
    filters: {
        search?: string;
        category?: string;
        condition?: string;
        price_min?: string;
        price_max?: string;
        sort?: string;
    };
}

export default function PartsIndex({ parts, categories, filters }: Props) {
    const [search, setSearch] = useState(filters.search || '');
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get('/pecas', { ...filters, search }, { preserveState: true });
    };

    const handleFilterChange = (key: string, value: string) => {
        router.get('/pecas', { ...filters, [key]: value }, { preserveState: true });
    };

    const getConditionBadge = (condition: string) => {
        const conditionConfig = {
            new: { label: 'Nova', variant: 'default' as const },
            used: { label: 'Usada', variant: 'secondary' as const },
            refurbished: { label: 'Recondicionada', variant: 'outline' as const },
        };

        const config = conditionConfig[condition as keyof typeof conditionConfig] || { label: condition, variant: 'secondary' as const };
        return <Badge variant={config.variant}>{config.label}</Badge>;
    };

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(price);
    };

    return (
        <>
            <Head title="Peças Automotivas" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Peças Automotivas</h1>
                        <p className="text-muted-foreground">
                            Encontre peças para seu veículo com os melhores preços
                        </p>
                    </div>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            Filtros
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                            {/* Search */}
                            <form onSubmit={handleSearch} className="lg:col-span-2">
                                <div className="flex gap-2">
                                    <Input
                                        placeholder="Buscar peças..."
                                        value={search}
                                        onChange={(e) => setSearch(e.target.value)}
                                    />
                                    <Button type="submit">
                                        <Search className="h-4 w-4" />
                                    </Button>
                                </div>
                            </form>

                            {/* Category */}
                            <Select 
                                value={filters.category || ''} 
                                onValueChange={(value) => handleFilterChange('category', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Categoria" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="">Todas as categorias</SelectItem>
                                    {categories.map((category) => (
                                        <SelectItem key={category.id} value={category.slug}>
                                            {category.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            {/* Condition */}
                            <Select 
                                value={filters.condition || ''} 
                                onValueChange={(value) => handleFilterChange('condition', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Condição" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="">Todas as condições</SelectItem>
                                    <SelectItem value="new">Nova</SelectItem>
                                    <SelectItem value="used">Usada</SelectItem>
                                    <SelectItem value="refurbished">Recondicionada</SelectItem>
                                </SelectContent>
                            </Select>

                            {/* Sort */}
                            <Select 
                                value={filters.sort || 'created_at'} 
                                onValueChange={(value) => handleFilterChange('sort', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Ordenar por" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="created_at">Mais recentes</SelectItem>
                                    <SelectItem value="price_asc">Menor preço</SelectItem>
                                    <SelectItem value="price_desc">Maior preço</SelectItem>
                                    <SelectItem value="views">Mais visualizadas</SelectItem>
                                    <SelectItem value="name">Nome A-Z</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </CardContent>
                </Card>

                {/* Results Header */}
                <div className="flex items-center justify-between">
                    <div className="text-sm text-muted-foreground">
                        Mostrando {parts.from} a {parts.to} de {parts.total} peças
                    </div>
                    <div className="flex items-center gap-2">
                        <Button
                            variant={viewMode === 'grid' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setViewMode('grid')}
                        >
                            <Grid3X3 className="h-4 w-4" />
                        </Button>
                        <Button
                            variant={viewMode === 'list' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setViewMode('list')}
                        >
                            <List className="h-4 w-4" />
                        </Button>
                    </div>
                </div>

                {/* Parts Grid/List */}
                {viewMode === 'grid' ? (
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                        {parts.data.map((part) => (
                            <Card key={part.id} className="group hover:shadow-lg transition-shadow">
                                <div className="relative">
                                    <ProductImage
                                        src={part.images[0]?.url}
                                        alt={part.name}
                                        className="h-48"
                                        featured={false}
                                    />
                                    <Button
                                        size="sm"
                                        variant="secondary"
                                        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                                    >
                                        <Heart className="h-4 w-4" />
                                    </Button>
                                </div>
                                <CardContent className="p-4">
                                    <div className="space-y-2">
                                        <div className="flex items-start justify-between">
                                            <h3 className="font-semibold text-lg line-clamp-2">
                                                <Link 
                                                    href={`/pecas/${part.slug}`}
                                                    className="hover:text-primary"
                                                >
                                                    {part.name}
                                                </Link>
                                            </h3>
                                            {getConditionBadge(part.condition)}
                                        </div>
                                        
                                        <p className="text-sm text-muted-foreground line-clamp-2">
                                            {part.description}
                                        </p>

                                        <div className="text-2xl font-bold text-primary">
                                            {formatPrice(part.price)}
                                        </div>

                                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                            <div className="flex items-center gap-1">
                                                <Eye className="h-3 w-3" />
                                                {part.views}
                                            </div>
                                            <div className="flex items-center gap-1">
                                                <Calendar className="h-3 w-3" />
                                                {new Date(part.created_at).toLocaleDateString()}
                                            </div>
                                        </div>

                                        <div className="flex items-center justify-between pt-2">
                                            <div className="text-sm">
                                                <p className="font-medium">{part.user.name}</p>
                                                {part.user.location && (
                                                    <div className="flex items-center gap-1 text-muted-foreground">
                                                        <MapPin className="h-3 w-3" />
                                                        {part.user.location}
                                                    </div>
                                                )}
                                            </div>
                                            <Badge variant="outline">{part.category.name}</Badge>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                ) : (
                    <div className="space-y-4">
                        {parts.data.map((part) => (
                            <Card key={part.id} className="hover:shadow-lg transition-shadow">
                                <CardContent className="p-6">
                                    <div className="flex gap-6">
                                        <div className="flex-shrink-0">
                                            <ProductImage
                                                src={part.images[0]?.url}
                                                alt={part.name}
                                                className="w-32 h-24"
                                                featured={false}
                                            />
                                        </div>
                                        <div className="flex-1 space-y-2">
                                            <div className="flex items-start justify-between">
                                                <div>
                                                    <h3 className="font-semibold text-xl">
                                                        <Link 
                                                            href={`/pecas/${part.slug}`}
                                                            className="hover:text-primary"
                                                        >
                                                            {part.name}
                                                        </Link>
                                                    </h3>
                                                    <div className="flex items-center gap-2 mt-1">
                                                        {getConditionBadge(part.condition)}
                                                        <Badge variant="outline">{part.category.name}</Badge>
                                                    </div>
                                                </div>
                                                <div className="text-right">
                                                    <div className="text-2xl font-bold text-primary">
                                                        {formatPrice(part.price)}
                                                    </div>
                                                    <Button size="sm" className="mt-2">
                                                        <Heart className="h-4 w-4 mr-2" />
                                                        Favoritar
                                                    </Button>
                                                </div>
                                            </div>
                                            
                                            <p className="text-muted-foreground line-clamp-2">
                                                {part.description}
                                            </p>

                                            <div className="flex items-center justify-between">
                                                <div className="text-sm">
                                                    <p className="font-medium">{part.user.name}</p>
                                                    {part.user.location && (
                                                        <div className="flex items-center gap-1 text-muted-foreground">
                                                            <MapPin className="h-3 w-3" />
                                                            {part.user.location}
                                                        </div>
                                                    )}
                                                </div>
                                                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                                    <div className="flex items-center gap-1">
                                                        <Eye className="h-3 w-3" />
                                                        {part.views}
                                                    </div>
                                                    <div className="flex items-center gap-1">
                                                        <Calendar className="h-3 w-3" />
                                                        {new Date(part.created_at).toLocaleDateString()}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                )}

                {/* Pagination */}
                {parts.last_page > 1 && (
                    <div className="flex items-center justify-center gap-2">
                        {parts.current_page > 1 && (
                            <Button 
                                variant="outline"
                                onClick={() => router.get('/pecas', { 
                                    ...filters, 
                                    page: parts.current_page - 1 
                                })}
                            >
                                Anterior
                            </Button>
                        )}
                        
                        <span className="text-sm text-muted-foreground">
                            Página {parts.current_page} de {parts.last_page}
                        </span>
                        
                        {parts.current_page < parts.last_page && (
                            <Button 
                                variant="outline"
                                onClick={() => router.get('/pecas', { 
                                    ...filters, 
                                    page: parts.current_page + 1 
                                })}
                            >
                                Próxima
                            </Button>
                        )}
                    </div>
                )}
            </div>
        </>
    );
}
