import CategoryGrid from '@/components/CategoryGrid';
import FeaturedListings from '@/components/FeaturedListings';
import HeroSection from '@/components/HeroSection';
import MainLayout from '@/layouts/MainLayout';
import { PageProps } from '@/types';
interface Category {
    id: number;
    name: string;
    slug: string;
    description: string;
    icon?: string;
    image?: string;
    url: string;
    vehicles_count?: number;
    parts_count?: number;
}

interface Vehicle {
    id: number;
    model: string;
    slug: string;
    price: number;
    promotional_price?: number;
    year_manufacture: number;
    mileage: number;
    color: string;
    fuel_type: string;
    transmission: string;
    is_featured: boolean;
    is_negotiable: boolean;
    main_image_url?: string;
    url: string;
    brand: {
        name: string;
    };
    category: {
        name: string;
        slug: string;
    };
}

interface Part {
    id: number;
    name: string;
    slug: string;
    price: number;
    promotional_price?: number;
    stock_quantity: number;
    is_original: boolean;
    is_featured: boolean;
    main_image_url?: string;
    url: string;
    brand: {
        name: string;
    };
}

interface WelcomeProps extends PageProps {
    categories: Category[];
    featuredVehicles?: Vehicle[];
    featuredParts?: Part[];
}

export default function Welcome({
    categories = [],
    featuredVehicles = [],
    featuredParts = [],
}: WelcomeProps) {
    return (
        <MainLayout categories={categories}>
            <div className="space-y-0">
                <HeroSection />

                <section className="bg-gray-50 py-12">
                    <div className="container mx-auto px-4">
                        <div className="mb-8 text-center">
                            <h2 className="mb-4 text-3xl font-bold text-gray-900">
                                Explore Nossas Categorias
                            </h2>
                            <p className="mx-auto max-w-2xl text-lg text-gray-600">
                                Encontre o que você precisa em nossas categorias
                                organizadas de veículos e peças
                            </p>
                        </div>
                        <CategoryGrid
                            categories={categories}
                            showCounts={true}
                        />
                    </div>
                </section>

                <FeaturedListings
                    vehicles={featuredVehicles}
                    parts={featuredParts}
                    title="Ofertas em Destaque"
                    showMoreLink="/anuncios"
                />

                <section className="bg-blue-600 py-12 text-white">
                    <div className="container mx-auto px-4 text-center">
                        <h2 className="mb-4 text-3xl font-bold">
                            Por que escolher nossa plataforma?
                        </h2>
                        <div className="mt-12 grid gap-8 md:grid-cols-3">
                            <div className="space-y-4">
                                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-yellow-500">
                                    <span className="text-2xl">🛡️</span>
                                </div>
                                <h3 className="text-xl font-semibold">
                                    Segurança Garantida
                                </h3>
                                <p className="text-blue-100">
                                    Todos os anúncios são verificados e temos
                                    sistema de proteção ao comprador
                                </p>
                            </div>
                            <div className="space-y-4">
                                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-yellow-500">
                                    <span className="text-2xl">⚡</span>
                                </div>
                                <h3 className="text-xl font-semibold">
                                    Rapidez e Eficiência
                                </h3>
                                <p className="text-blue-100">
                                    Encontre o que precisa em minutos e feche
                                    negócios com rapidez
                                </p>
                            </div>
                            <div className="space-y-4">
                                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-yellow-500">
                                    <span className="text-2xl">💎</span>
                                </div>
                                <h3 className="text-xl font-semibold">
                                    Qualidade Premium
                                </h3>
                                <p className="text-blue-100">
                                    Trabalhamos apenas com vendedores e peças de
                                    alta qualidade
                                </p>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </MainLayout>
    );
}
