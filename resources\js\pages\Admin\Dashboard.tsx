import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
    Users, 
    Car, 
    Settings, 
    ShoppingBag, 
    TrendingUp, 
    TrendingDown,
    Eye,
    MessageSquare,
    AlertTriangle,
    CheckCircle
} from 'lucide-react';
import { Link } from '@inertiajs/react';
import { ResponsiveContainer, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, BarChart, Bar } from 'recharts';

interface DashboardStats {
    total_users: number;
    active_users: number;
    total_vehicles: number;
    published_vehicles: number;
    total_parts: number;
    active_parts: number;
    total_advertisements: number;
    pending_advertisements: number;
    total_views: number;
    total_messages: number;
}

interface ChartData {
    name: string;
    users: number;
    vehicles: number;
    parts: number;
    advertisements: number;
}

interface RecentItem {
    id: number;
    title: string;
    type: 'user' | 'vehicle' | 'part' | 'advertisement';
    status: string;
    created_at: string;
    user?: {
        name: string;
        email: string;
    };
}

interface Props {
    stats: DashboardStats;
    chartData: ChartData[];
    recentUsers: RecentItem[];
    recentVehicles: RecentItem[];
    recentParts: RecentItem[];
    recentAdvertisements: RecentItem[];
}

export default function AdminDashboard({ 
    stats, 
    chartData, 
    recentUsers, 
    recentVehicles, 
    recentParts, 
    recentAdvertisements 
}: Props) {
    const statCards = [
        {
            title: 'Total de Usuários',
            value: stats.total_users,
            description: `${stats.active_users} ativos`,
            icon: Users,
            color: 'text-blue-600',
            bgColor: 'bg-blue-50',
            trend: stats.active_users > 0 ? 'up' : 'down',
        },
        {
            title: 'Veículos',
            value: stats.total_vehicles,
            description: `${stats.published_vehicles} publicados`,
            icon: Car,
            color: 'text-green-600',
            bgColor: 'bg-green-50',
            trend: stats.published_vehicles > 0 ? 'up' : 'down',
        },
        {
            title: 'Peças',
            value: stats.total_parts,
            description: `${stats.active_parts} ativas`,
            icon: Settings,
            color: 'text-purple-600',
            bgColor: 'bg-purple-50',
            trend: stats.active_parts > 0 ? 'up' : 'down',
        },
        {
            title: 'Anúncios',
            value: stats.total_advertisements,
            description: `${stats.pending_advertisements} pendentes`,
            icon: ShoppingBag,
            color: 'text-orange-600',
            bgColor: 'bg-orange-50',
            trend: stats.pending_advertisements > 0 ? 'down' : 'up',
        },
        {
            title: 'Visualizações',
            value: stats.total_views,
            description: 'Total de visualizações',
            icon: Eye,
            color: 'text-indigo-600',
            bgColor: 'bg-indigo-50',
            trend: 'up',
        },
        {
            title: 'Mensagens',
            value: stats.total_messages,
            description: 'Total de mensagens',
            icon: MessageSquare,
            color: 'text-pink-600',
            bgColor: 'bg-pink-50',
            trend: 'up',
        },
    ];

    const getStatusBadge = (status: string) => {
        const statusConfig = {
            active: { label: 'Ativo', variant: 'default' as const },
            inactive: { label: 'Inativo', variant: 'secondary' as const },
            pending: { label: 'Pendente', variant: 'destructive' as const },
            published: { label: 'Publicado', variant: 'default' as const },
            draft: { label: 'Rascunho', variant: 'secondary' as const },
            sold: { label: 'Vendido', variant: 'outline' as const },
        };

        const config = statusConfig[status as keyof typeof statusConfig] || { label: status, variant: 'secondary' as const };
        return <Badge variant={config.variant}>{config.label}</Badge>;
    };

    return (
        <>
            <Head title="Dashboard Admin" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Dashboard Administrativo</h1>
                        <p className="text-muted-foreground">
                            Visão geral do sistema e estatísticas principais
                        </p>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {statCards.map((stat, index) => (
                        <Card key={index}>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">
                                    {stat.title}
                                </CardTitle>
                                <div className={`p-2 rounded-full ${stat.bgColor}`}>
                                    <stat.icon className={`h-4 w-4 ${stat.color}`} />
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stat.value.toLocaleString()}</div>
                                <div className="flex items-center text-xs text-muted-foreground">
                                    {stat.trend === 'up' ? (
                                        <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                                    ) : (
                                        <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
                                    )}
                                    {stat.description}
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {/* Charts */}
                <div className="grid gap-4 md:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Crescimento por Período</CardTitle>
                            <CardDescription>
                                Evolução de usuários, veículos e peças
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={300}>
                                <LineChart data={chartData}>
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis dataKey="name" />
                                    <YAxis />
                                    <Tooltip />
                                    <Line type="monotone" dataKey="users" stroke="#3b82f6" strokeWidth={2} />
                                    <Line type="monotone" dataKey="vehicles" stroke="#10b981" strokeWidth={2} />
                                    <Line type="monotone" dataKey="parts" stroke="#8b5cf6" strokeWidth={2} />
                                </LineChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Distribuição de Conteúdo</CardTitle>
                            <CardDescription>
                                Comparação entre tipos de conteúdo
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={300}>
                                <BarChart data={chartData}>
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis dataKey="name" />
                                    <YAxis />
                                    <Tooltip />
                                    <Bar dataKey="vehicles" fill="#10b981" />
                                    <Bar dataKey="parts" fill="#8b5cf6" />
                                    <Bar dataKey="advertisements" fill="#f59e0b" />
                                </BarChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>
                </div>

                {/* Recent Activity */}
                <div className="grid gap-4 md:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Usuários Recentes</CardTitle>
                            <CardDescription>
                                Últimos usuários cadastrados
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {recentUsers.map((user) => (
                                    <div key={user.id} className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium">{user.title}</p>
                                            <p className="text-xs text-muted-foreground">
                                                {new Date(user.created_at).toLocaleDateString()}
                                            </p>
                                        </div>
                                        {getStatusBadge(user.status)}
                                    </div>
                                ))}
                                <Button asChild variant="outline" className="w-full">
                                    <Link href="/admin/usuarios">Ver Todos</Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Anúncios Pendentes</CardTitle>
                            <CardDescription>
                                Anúncios aguardando moderação
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {recentAdvertisements.filter(ad => ad.status === 'pending').map((ad) => (
                                    <div key={ad.id} className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium">{ad.title}</p>
                                            <p className="text-xs text-muted-foreground">
                                                {ad.user?.name} • {new Date(ad.created_at).toLocaleDateString()}
                                            </p>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            {getStatusBadge(ad.status)}
                                            <AlertTriangle className="h-4 w-4 text-orange-500" />
                                        </div>
                                    </div>
                                ))}
                                <Button asChild variant="outline" className="w-full">
                                    <Link href="/admin/anuncios">Moderar Anúncios</Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </>
    );
}
