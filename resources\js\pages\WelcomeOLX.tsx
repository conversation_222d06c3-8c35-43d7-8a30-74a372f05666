import HeroSectionOLX from '@/components/HeroSectionOLX';
import CategoryGrid from '@/components/CategoryGrid';
import ProductGrid from '@/components/ProductGrid';
import MainLayout from '@/layouts/MainLayout';
import { PageProps } from '@/types';

interface Category {
    id: number;
    name: string;
    slug: string;
    description: string;
    icon?: string;
    image?: string;
    url: string;
    vehicles_count?: number;
    parts_count?: number;
}

interface Vehicle {
    id: number;
    title: string;
    slug: string;
    price: number;
    promotional_price?: number;
    year_manufacture: number;
    mileage: number;
    color: string;
    fuel_type: string;
    transmission: string;
    is_featured: boolean;
    is_negotiable: boolean;
    location: string;
    created_at: string;
    main_image?: {
        url: string;
    };
    url: string;
    brand: {
        name: string;
    };
    category: {
        name: string;
        slug: string;
    };
}

interface WelcomeProps extends PageProps {
    categories: Category[];
    featuredListings?: Vehicle[];
}

export default function WelcomeOLX({ 
    categories = [], 
    featuredListings = [] 
}: WelcomeProps) {
    return (
        <MainLayout categories={categories}>
            <div className="space-y-0">
                <HeroSectionOLX />
                
                <section className="py-12 bg-gray-50">
                    <div className="container mx-auto px-4">
                        <div className="text-center mb-8">
                            <h2 className="text-3xl font-bold text-gray-900 mb-4">
                                Explore Nossas Categorias
                            </h2>
                            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                                Encontre o que você precisa em nossas categorias organizadas de veículos e peças
                            </p>
                        </div>
                        <CategoryGrid 
                            categories={categories} 
                            showCounts={true}
                        />
                    </div>
                </section>

                <ProductGrid 
                    title="Mais procurados em Calçados"
                    products={featuredListings}
                    showMoreLink="/anuncios"
                />

                <section className="py-12 bg-blue-600 text-white">
                    <div className="container mx-auto px-4 text-center">
                        <h2 className="text-3xl font-bold mb-4">
                            Por que escolher nossa plataforma?
                        </h2>
                        <div className="grid md:grid-cols-3 gap-8 mt-12">
                            <div className="space-y-4">
                                <div className="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mx-auto">
                                    <span className="text-2xl">🛡️</span>
                                </div>
                                <h3 className="text-xl font-semibold">Segurança Garantida</h3>
                                <p className="text-blue-100">
                                    Todos os anúncios são verificados e temos sistema de proteção ao comprador
                                </p>
                            </div>
                            <div className="space-y-4">
                                <div className="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mx-auto">
                                    <span className="text-2xl">⚡</span>
                                </div>
                                <h3 className="text-xl font-semibold">Rapidez e Eficiência</h3>
                                <p className="text-blue-100">
                                    Encontre o que precisa em minutos e feche negócios com rapidez
                                </p>
                            </div>
                            <div className="space-y-4">
                                <div className="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mx-auto">
                                    <span className="text-2xl">💎</span>
                                </div>
                                <h3 className="text-xl font-semibold">Qualidade Premium</h3>
                                <p className="text-blue-100">
                                    Trabalhamos apenas com vendedores e peças de alta qualidade
                                </p>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </MainLayout>
    );
}
