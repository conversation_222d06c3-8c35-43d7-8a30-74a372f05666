import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Link, router } from '@inertiajs/react';
import { Bell, Menu, MessageCircle, Plus, Search, User } from 'lucide-react';
import { useState } from 'react';

export interface Category {
    id: number;
    name: string;
    slug: string;
    description?: string;
    icon?: string;
    children: Category[];
    listings?: Array<{
        id: number;
        title: string;
        price: number;
        location: string;
        created_at: string;
        main_image?: {
            url: string;
        };
        category: {
            name: string;
        };
        brand: {
            name: string;
        };
    }>;
}

interface HeaderProps {
    categories?: Category[];
    auth?: {
        user?: {
            id: number;
            name: string;
            email: string;
        };
    };
}

export default function Header({ categories = [], auth }: HeaderProps) {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [notificationCount] = useState(0);
    const isAuthenticated = !!auth?.user;

    return (
        <header className="w-full border-b border-gray-200 bg-white">
            <div className="container mx-auto px-4">
                <div className="flex h-16 items-center justify-between">
                    {/* Logo */}
                    <div className="flex items-center space-x-8">
                        <Link href="/" className="flex items-center">
                            <div className="text-2xl font-bold">
                                <span className="text-primary">O</span>
                                <span className="text-secondary">L</span>
                                <span className="text-primary">X</span>
                            </div>
                        </Link>

                        {/* Desktop Category Navigation */}
                        <nav className="hidden space-x-6 lg:flex">
                            <Link
                                href="/"
                                className="font-medium text-gray-700 transition-colors hover:text-primary"
                            >
                                Início
                            </Link>
                            {categories.length > 0 && (
                                <div className="group relative">
                                    <button className="flex items-center font-medium text-gray-700 transition-colors hover:text-primary">
                                        Categorias
                                        <svg
                                            className="ml-1 h-4 w-4"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M19 9l-7 7-7-7"
                                            />
                                        </svg>
                                    </button>
                                    <div className="invisible absolute left-0 z-50 mt-2 w-64 rounded-lg border border-gray-200 bg-white opacity-0 shadow-lg transition-all duration-200 group-hover:visible group-hover:opacity-100">
                                        <div className="py-2">
                                            {categories.map(
                                                (category: Category) => (
                                                    <Link
                                                        key={category.id}
                                                        href={`/categorias/${category.slug}`}
                                                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-primary"
                                                    >
                                                        {category.name}
                                                    </Link>
                                                ),
                                            )}
                                        </div>
                                    </div>
                                </div>
                            )}
                            <Link
                                href="/veiculos"
                                className="font-medium text-gray-700 transition-colors hover:text-primary"
                            >
                                Veículos
                            </Link>
                        </nav>
                    </div>

                    {/* Search Bar - Desktop */}
                    <div className="mx-8 hidden max-w-2xl flex-1 md:flex">
                        <div className="relative flex-1">
                            <Input
                                type="text"
                                placeholder='Buscar "Celta"'
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                        handleSearch();
                                    }
                                }}
                                className="h-10 rounded-l-md rounded-r-none border-gray-300 pr-12 pl-4 focus:border-primary focus:ring-primary"
                            />
                            <Button
                                size="sm"
                                onClick={handleSearch}
                                className="absolute top-0 right-0 h-10 rounded-l-none bg-primary px-4 hover:bg-primary/90"
                            >
                                <Search className="h-4 w-4" />
                            </Button>
                        </div>
                    </div>

                    {/* Desktop Actions */}
                    <div className="hidden items-center space-x-4 md:flex">
                        {isAuthenticated ? (
                            <>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() =>
                                        router.get('/planos/profissional')
                                    }
                                    className="text-gray-600"
                                >
                                    Plano Profissional
                                </Button>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => router.get('/meus-anuncios')}
                                    className="text-gray-600"
                                >
                                    Meus Anúncios
                                </Button>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => router.get('/chat')}
                                    className="relative text-gray-600"
                                >
                                    <MessageCircle className="mr-1 h-4 w-4" />
                                    Chat
                                </Button>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => router.get('/notificacoes')}
                                    className="relative text-gray-600"
                                >
                                    <Bell className="mr-1 h-4 w-4" />
                                    Notificações
                                    {notificationCount > 0 && (
                                        <Badge className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-secondary p-0 text-xs text-white">
                                            {notificationCount}
                                        </Badge>
                                    )}
                                </Button>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => router.get('/perfil')}
                                    className="text-gray-600"
                                >
                                    <User className="mr-1 h-4 w-4" />
                                    {auth?.user?.name || 'Usuário'}
                                </Button>
                                <Button
                                    onClick={() => router.get('/anunciar')}
                                    className="bg-secondary px-6 text-white hover:bg-secondary/90"
                                >
                                    <Plus className="mr-2 h-4 w-4" />
                                    Anunciar grátis
                                </Button>
                            </>
                        ) : (
                            <>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => router.get('/login')}
                                    className="text-gray-600"
                                >
                                    Entrar
                                </Button>
                                <Button
                                    onClick={() => router.get('/register')}
                                    className="bg-secondary px-6 text-white hover:bg-secondary/90"
                                >
                                    Cadastrar
                                </Button>
                            </>
                        )}
                    </div>

                    {/* Mobile menu button */}
                    <div className="flex items-center md:hidden">
                        <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => setIsMenuOpen(!isMenuOpen)}
                        >
                            <Menu className="h-6 w-6" />
                            <span className="sr-only">Abrir menu</span>
                        </Button>
                    </div>

                    {/* Mobile menu */}
                    <div
                        className={`md:hidden ${isMenuOpen ? 'block' : 'hidden'}`}
                        onClick={() => setIsMenuOpen(false)}
                    >
                        <div className="space-y-1 pt-2 pb-3">
                            <Link
                                href="/"
                                className="block border-l-4 border-transparent py-2 pr-4 pl-3 text-base font-medium text-gray-600 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-800"
                            >
                                Início
                            </Link>
                            {categories.length > 0 && (
                                <div className="space-y-1">
                                    <div className="border-l-4 border-transparent py-2 pr-4 pl-3 text-base font-medium text-gray-600">
                                        Categorias
                                    </div>
                                    <div className="ml-4 space-y-1">
                                        {categories.map(
                                            (category: Category) => (
                                                <Link
                                                    key={category.id}
                                                    href={`/categorias/${category.slug}`}
                                                    className="block border-l-4 border-transparent py-2 pr-4 pl-3 text-sm font-medium text-gray-600 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-800"
                                                >
                                                    {category.name}
                                                </Link>
                                            ),
                                        )}
                                    </div>
                                </div>
                            )}
                            <Link
                                href="/veiculos"
                                className="block border-l-4 border-transparent py-2 pr-4 pl-3 text-base font-medium text-gray-600 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-800"
                            >
                                Veículos
                            </Link>
                        </div>
                    </div>
                </div>

                {/* Mobile Search */}
                <div className="py-3 md:hidden">
                    <div className="relative">
                        <Input
                            type="text"
                            placeholder='Buscar "Celta"'
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                    handleSearch();
                                }
                            }}
                            className="h-10 border-gray-300 pr-12 pl-4 focus:border-primary focus:ring-primary"
                        />
                        <Button
                            size="sm"
                            onClick={handleSearch}
                            className="absolute top-1 right-1 h-8 bg-primary px-3 hover:bg-primary/90"
                        >
                            <Search className="h-4 w-4" />
                        </Button>
                    </div>
                </div>

                {/* Mobile Navigation Menu */}
                {isMenuOpen && (
                    <div className="border-t border-gray-100 py-4 md:hidden">
                        <nav className="flex flex-col space-y-2">
                            {isAuthenticated ? (
                                <>
                                    <Button
                                        variant="ghost"
                                        onClick={() =>
                                            router.get('/meus-anuncios')
                                        }
                                        className="justify-start text-gray-600"
                                    >
                                        Meus Anúncios
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        onClick={() => router.get('/chat')}
                                        className="justify-start text-gray-600"
                                    >
                                        Chat
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        onClick={() =>
                                            router.get('/notificacoes')
                                        }
                                        className="justify-start text-gray-600"
                                    >
                                        Notificações
                                    </Button>
                                    <Button
                                        onClick={() => router.get('/anunciar')}
                                        className="mt-4 justify-start bg-secondary text-white hover:bg-secondary/90"
                                    >
                                        <Plus className="mr-2 h-4 w-4" />
                                        Anunciar grátis
                                    </Button>
                                </>
                            ) : (
                                <>
                                    <Button
                                        variant="ghost"
                                        onClick={() => router.get('/login')}
                                        className="justify-start text-gray-600"
                                    >
                                        Entrar
                                    </Button>
                                    <Button
                                        onClick={() => router.get('/register')}
                                        className="justify-start bg-secondary text-white hover:bg-secondary/90"
                                    >
                                        Cadastrar
                                    </Button>
                                </>
                            )}
                        </nav>
                    </div>
                )}
            </div>
        </header>
    );

    function handleSearch() {
        if (searchQuery.trim()) {
            router.get('/pesquisar', { search: searchQuery });
            setSearchQuery('');
        }
    }
}
