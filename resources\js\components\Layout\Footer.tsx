import { Link } from '@inertiajs/react';
import {
    FaEnvelope,
    FaFacebook,
    FaInstagram,
    FaLinkedin,
    FaMapMarkerAlt,
    FaPhone,
    FaTwitter,
} from 'react-icons/fa';

type FooterLink = {
    title: string;
    links: Array<{
        name: string;
        href: string;
        external?: boolean;
    }>;
};

const footerLinks: FooterLink[] = [
    {
        title: 'Navegação',
        links: [
            { name: '<PERSON><PERSON><PERSON>', href: '/' },
            { name: '<PERSON><PERSON><PERSON><PERSON>', href: '/anuncios' },
            { name: 'Categorias', href: '/pesquisar' },
            { name: 'Como funciona', href: '/como-funciona' },
            { name: 'Preç<PERSON>', href: '/planos' },
        ],
    },
    {
        title: 'Empresa',
        links: [
            { name: 'Sobre nós', href: '/sobre' },
            { name: '<PERSON><PERSON><PERSON> de uso', href: '/termos' },
            { name: '<PERSON>í<PERSON> de privacidade', href: '/privacidade' },
            { name: 'Trabal<PERSON> conosco', href: '/trabalhe-conosco' },
            { name: '<PERSON><PERSON><PERSON>', href: '/contato' },
        ],
    },
    {
        title: 'Ajuda',
        links: [
            { name: 'Central de Ajuda', href: '/ajuda' },
            { name: 'Dúvidas frequentes', href: '/faq' },
            { name: 'Segurança', href: '/seguranca' },
            { name: 'Denunciar um problema', href: '/denunciar' },
        ],
    },
];

export default function Footer() {
    const currentYear = new Date().getFullYear();

    return (
        <footer className="bg-gray-900 pt-16 pb-8 text-white">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
                    {/* Sobre */}
                    <div className="col-span-1 lg:col-span-1">
                        <h3 className="mb-4 text-2xl font-bold">AutoMercado</h3>
                        <p className="mb-6 text-gray-400">
                            A plataforma mais completa para compra e venda de
                            veículos e peças automotivas.
                        </p>
                        <div className="flex space-x-4">
                            <a
                                href="https://facebook.com"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-gray-400 transition-colors hover:text-white"
                                aria-label="Facebook"
                            >
                                <FaFacebook className="h-6 w-6" />
                            </a>
                            <a
                                href="https://instagram.com"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-gray-400 transition-colors hover:text-white"
                                aria-label="Instagram"
                            >
                                <FaInstagram className="h-6 w-6" />
                            </a>
                            <a
                                href="https://twitter.com"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-gray-400 transition-colors hover:text-white"
                                aria-label="Twitter"
                            >
                                <FaTwitter className="h-6 w-6" />
                            </a>
                            <a
                                href="https://linkedin.com"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-gray-400 transition-colors hover:text-white"
                                aria-label="LinkedIn"
                            >
                                <FaLinkedin className="h-6 w-6" />
                            </a>
                        </div>
                    </div>

                    {/* Links de navegação */}
                    {footerLinks.map((section, index) => (
                        <div key={index} className="mt-6 md:mt-0">
                            <h4 className="mb-4 text-lg font-semibold">
                                {section.title}
                            </h4>
                            <ul className="space-y-2">
                                {section.links.map((link, linkIndex) => (
                                    <li key={linkIndex}>
                                        {link.external ? (
                                            <a
                                                href={link.href}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-gray-400 transition-colors hover:text-white"
                                            >
                                                {link.name}
                                            </a>
                                        ) : (
                                            <Link
                                                href={link.href}
                                                className="text-gray-400 transition-colors hover:text-white"
                                            >
                                                {link.name}
                                            </Link>
                                        )}
                                    </li>
                                ))}
                            </ul>
                        </div>
                    ))}

                    {/* Contato */}
                    <div className="mt-6 md:mt-0">
                        <h4 className="mb-4 text-lg font-semibold">Contato</h4>
                        <ul className="space-y-3">
                            <li className="flex items-start">
                                <FaMapMarkerAlt className="mt-1 mr-3 flex-shrink-0 text-orange-500" />
                                <span className="text-gray-400">
                                    Av. Paulista, 1000
                                    <br />
                                    São Paulo - SP, 01310-100
                                </span>
                            </li>
                            <li className="flex items-center">
                                <FaPhone className="mr-3 text-orange-500" />
                                <a
                                    href="tel:+5511999999999"
                                    className="text-gray-400 transition-colors hover:text-white"
                                >
                                    (11) 99999-9999
                                </a>
                            </li>
                            <li className="flex items-center">
                                <FaEnvelope className="mr-3 text-orange-500" />
                                <a
                                    href="mailto:<EMAIL>"
                                    className="text-gray-400 transition-colors hover:text-white"
                                >
                                    <EMAIL>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>

                <div className="mt-12 flex flex-col items-center justify-between border-t border-gray-800 pt-8 md:flex-row">
                    <p className="text-sm text-gray-400">
                        &copy; {currentYear} AutoMercado. Todos os direitos
                        reservados.
                    </p>
                    <div className="mt-4 flex space-x-6 md:mt-0">
                        <Link
                            href="/termos"
                            className="text-sm text-gray-400 hover:text-white"
                        >
                            Termos de Uso
                        </Link>
                        <Link
                            href="/privacidade"
                            className="text-sm text-gray-400 hover:text-white"
                        >
                            Política de Privacidade
                        </Link>
                        <Link
                            href="/cookies"
                            className="text-sm text-gray-400 hover:text-white"
                        >
                            Política de Cookies
                        </Link>
                    </div>
                </div>

                <div className="mt-8 text-center">
                    <p className="text-xs text-gray-500">
                        AutoMercado é uma plataforma de compra e venda de
                        veículos e peças automotivas. Não nos responsabilizamos
                        pelos anúncios publicados por usuários.
                    </p>
                </div>
            </div>
        </footer>
    );
}
