<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Advertisement;
use App\Models\Offer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class OfferController extends Controller
{
    /**
     * Display a listing of the user's offers.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        $query = Offer::with(['advertisement.user', 'advertisement.images', 'user'])
            ->where(function ($q) use ($user) {
                $q->where('user_id', $user->id)
                  ->orWhereHas('advertisement', function ($subQ) use ($user) {
                      $subQ->where('user_id', $user->id);
                  });
            });

        // Filter by type (sent/received)
        if ($request->type === 'sent') {
            $query->where('user_id', $user->id);
        } elseif ($request->type === 'received') {
            $query->whereHas('advertisement', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            });
        }

        // Filter by status
        if ($request->status) {
            $query->where('status', $request->status);
        }

        $offers = $query->orderBy('created_at', 'desc')->paginate(20);

        $stats = [
            'total' => Offer::where('user_id', $user->id)->count(),
            'pending' => Offer::where('user_id', $user->id)->where('status', 'pending')->count(),
            'accepted' => Offer::where('user_id', $user->id)->where('status', 'accepted')->count(),
            'rejected' => Offer::where('user_id', $user->id)->where('status', 'rejected')->count(),
            'received_total' => Offer::whereHas('advertisement', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            })->count(),
            'received_pending' => Offer::whereHas('advertisement', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            })->where('status', 'pending')->count(),
        ];

        return Inertia::render('User/Offers/Index', [
            'offers' => $offers,
            'stats' => $stats,
            'filters' => [
                'type' => $request->type,
                'status' => $request->status,
            ],
        ]);
    }

    /**
     * Show the form for creating a new offer.
     */
    public function create(Request $request, Advertisement $advertisement)
    {
        // Check if user already has a pending offer for this advertisement
        $existingOffer = Offer::where('user_id', Auth::id())
            ->where('advertisement_id', $advertisement->id)
            ->where('status', 'pending')
            ->first();

        if ($existingOffer) {
            return redirect()->back()->with('error', 'Você já tem uma oferta pendente para este anúncio.');
        }

        return Inertia::render('User/Offers/Create', [
            'advertisement' => $advertisement->load(['user', 'images', 'vehicle.brand', 'vehicle.model']),
        ]);
    }

    /**
     * Store a newly created offer in storage.
     */
    public function store(Request $request, Advertisement $advertisement)
    {
        $request->validate([
            'amount' => 'required|numeric|min:1',
            'message' => 'nullable|string|max:1000',
        ]);

        // Check if user is trying to make an offer on their own advertisement
        if ($advertisement->user_id === Auth::id()) {
            return redirect()->back()->with('error', 'Você não pode fazer uma oferta no seu próprio anúncio.');
        }

        // Check if user already has a pending offer for this advertisement
        $existingOffer = Offer::where('user_id', Auth::id())
            ->where('advertisement_id', $advertisement->id)
            ->where('status', 'pending')
            ->first();

        if ($existingOffer) {
            return redirect()->back()->with('error', 'Você já tem uma oferta pendente para este anúncio.');
        }

        $offer = Offer::create([
            'user_id' => Auth::id(),
            'advertisement_id' => $advertisement->id,
            'amount' => $request->amount,
            'message' => $request->message,
            'status' => 'pending',
        ]);

        // Send notification to advertisement owner
        $advertisement->user->notify(new \App\Notifications\NewOffer($offer));

        return redirect()->route('user.offers.index')->with('success', 'Oferta enviada com sucesso!');
    }

    /**
     * Display the specified offer.
     */
    public function show(Offer $offer)
    {
        $user = Auth::user();
        
        // Check if user is authorized to view this offer
        if ($offer->user_id !== $user->id && $offer->advertisement->user_id !== $user->id) {
            abort(403);
        }

        $offer->load(['advertisement.user', 'advertisement.images', 'advertisement.vehicle.brand', 'advertisement.vehicle.model', 'user']);

        return Inertia::render('User/Offers/Show', [
            'offer' => $offer,
            'canRespond' => $offer->advertisement->user_id === $user->id && $offer->status === 'pending',
        ]);
    }

    /**
     * Accept an offer.
     */
    public function accept(Offer $offer)
    {
        $user = Auth::user();
        
        // Check if user is the advertisement owner
        if ($offer->advertisement->user_id !== $user->id) {
            abort(403);
        }

        if ($offer->status !== 'pending') {
            return redirect()->back()->with('error', 'Esta oferta não pode mais ser aceita.');
        }

        $offer->update([
            'status' => 'accepted',
            'responded_at' => now(),
        ]);

        // Reject all other pending offers for this advertisement
        Offer::where('advertisement_id', $offer->advertisement_id)
            ->where('id', '!=', $offer->id)
            ->where('status', 'pending')
            ->update([
                'status' => 'rejected',
                'responded_at' => now(),
            ]);

        // Send notification to offer maker
        $offer->user->notify(new \App\Notifications\OfferAccepted($offer));

        return redirect()->back()->with('success', 'Oferta aceita com sucesso!');
    }

    /**
     * Reject an offer.
     */
    public function reject(Request $request, Offer $offer)
    {
        $user = Auth::user();
        
        // Check if user is the advertisement owner
        if ($offer->advertisement->user_id !== $user->id) {
            abort(403);
        }

        if ($offer->status !== 'pending') {
            return redirect()->back()->with('error', 'Esta oferta não pode mais ser rejeitada.');
        }

        $offer->update([
            'status' => 'rejected',
            'rejection_reason' => $request->reason,
            'responded_at' => now(),
        ]);

        // Send notification to offer maker
        $offer->user->notify(new \App\Notifications\OfferRejected($offer));

        return redirect()->back()->with('success', 'Oferta rejeitada.');
    }

    /**
     * Cancel an offer (by the offer maker).
     */
    public function cancel(Offer $offer)
    {
        $user = Auth::user();
        
        // Check if user is the offer maker
        if ($offer->user_id !== $user->id) {
            abort(403);
        }

        if ($offer->status !== 'pending') {
            return redirect()->back()->with('error', 'Esta oferta não pode mais ser cancelada.');
        }

        $offer->update([
            'status' => 'cancelled',
            'responded_at' => now(),
        ]);

        return redirect()->back()->with('success', 'Oferta cancelada.');
    }

    /**
     * Counter offer (by the advertisement owner).
     */
    public function counter(Request $request, Offer $offer)
    {
        $user = Auth::user();
        
        // Check if user is the advertisement owner
        if ($offer->advertisement->user_id !== $user->id) {
            abort(403);
        }

        if ($offer->status !== 'pending') {
            return redirect()->back()->with('error', 'Não é possível fazer uma contra-oferta para esta oferta.');
        }

        $request->validate([
            'amount' => 'required|numeric|min:1',
            'message' => 'nullable|string|max:1000',
        ]);

        // Create a new offer as counter-offer
        $counterOffer = Offer::create([
            'user_id' => $user->id,
            'advertisement_id' => $offer->advertisement_id,
            'amount' => $request->amount,
            'message' => $request->message,
            'status' => 'pending',
            'parent_offer_id' => $offer->id,
        ]);

        // Mark original offer as countered
        $offer->update([
            'status' => 'countered',
            'responded_at' => now(),
        ]);

        // Send notification to original offer maker
        $offer->user->notify(new \App\Notifications\OfferCountered($counterOffer));

        return redirect()->back()->with('success', 'Contra-oferta enviada com sucesso!');
    }

    /**
     * Get offer statistics for dashboard.
     */
    public function getStats()
    {
        $user = Auth::user();
        
        $stats = [
            'sent' => [
                'total' => Offer::where('user_id', $user->id)->count(),
                'pending' => Offer::where('user_id', $user->id)->where('status', 'pending')->count(),
                'accepted' => Offer::where('user_id', $user->id)->where('status', 'accepted')->count(),
                'rejected' => Offer::where('user_id', $user->id)->where('status', 'rejected')->count(),
            ],
            'received' => [
                'total' => Offer::whereHas('advertisement', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->count(),
                'pending' => Offer::whereHas('advertisement', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->where('status', 'pending')->count(),
                'accepted' => Offer::whereHas('advertisement', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->where('status', 'accepted')->count(),
                'rejected' => Offer::whereHas('advertisement', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->where('status', 'rejected')->count(),
            ],
        ];

        return response()->json($stats);
    }
}
