import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import MainLayout from '@/layouts/MainLayout';
import { formatCurrency } from '@/lib/utils';
import type { Brand, Category, Vehicle } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { ArrowRight, Filter, Heart, Phone, Search, Star } from 'lucide-react';
import { useState } from 'react';

// Definir o tipo PageProps localmente
type PageProps = {
    auth: {
        user: {
            id: number;
            name: string;
            email: string;
            [key: string]: unknown;
        } | null;
    };
    [key: string]: unknown;
};

interface VehicleIndexProps extends PageProps {
    vehicles: {
        data: Vehicle[];
        current_page: number;
        first_page_url: string;
        from: number;
        last_page: number;
        last_page_url: string;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
        next_page_url: string | null;
        path: string;
        per_page: number;
        prev_page_url: string | null;
        to: number;
        total: number;
    };
    filters: {
        search?: string;
        brand?: string;
        category?: string;
        min_price?: string;
        max_price?: string;
        fuel_type?: string;
        transmission?: string;
    };
    brands: Brand[];
    categories: Category[];
    fuelTypes: Record<string, string>;
    transmissions: Record<string, string>;
}

export default function VehicleIndex({
    vehicles,
    filters,
    brands,
    categories,
    fuelTypes,
    transmissions,
}: VehicleIndexProps) {
    const [search, setSearch] = useState(filters.search || '');
    const [selectedBrand, setSelectedBrand] = useState(filters.brand || '');
    const [selectedCategory, setSelectedCategory] = useState(
        filters.category || '',
    );
    const [minPrice, setMinPrice] = useState(filters.min_price || '');
    const [maxPrice, setMaxPrice] = useState(filters.max_price || '');
    const [selectedFuelType, setSelectedFuelType] = useState(
        filters.fuel_type || '',
    );
    const [selectedTransmission, setSelectedTransmission] = useState(
        filters.transmission || '',
    );
    const [showFilters, setShowFilters] = useState(false);

    const handleFilter = () => {
        const params: Record<string, string> = {};

        if (search) params.search = search;
        if (selectedBrand) params.brand = selectedBrand;
        if (selectedCategory) params.category = selectedCategory;
        if (minPrice) params.min_price = minPrice;
        if (maxPrice) params.max_price = maxPrice;
        if (selectedFuelType) params.fuel_type = selectedFuelType;
        if (selectedTransmission) params.transmission = selectedTransmission;

        router.get(route('vehicles.index'), params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const resetFilters = () => {
        setSearch('');
        setSelectedBrand('');
        setSelectedCategory('');
        setMinPrice('');
        setMaxPrice('');
        setSelectedFuelType('');
        setSelectedTransmission('');

        router.get(
            route('vehicles.index'),
            {},
            {
                preserveState: true,
                preserveScroll: true,
            },
        );
    };

    return (
        <MainLayout>
            <Head title="Veículos em Destaque" />

            {/* Hero Section */}
            <div className="bg-gradient-to-r from-primary to-primary/90 py-16 text-primary-foreground">
                <div className="container">
                    <h1 className="mb-4 text-3xl font-bold md:text-4xl">
                        Encontre o carro dos seus sonhos
                    </h1>
                    <p className="max-w-2xl text-lg text-primary-foreground/90">
                        Explore nossa seleção de veículos seminovos e usados com
                        as melhores ofertas do mercado.
                    </p>
                </div>
            </div>

            {/* Filtros */}
            <div className="relative z-10 container -mt-8">
                <Card className="shadow-lg">
                    <CardContent className="p-4">
                        <div className="space-y-4">
                            {/* Barra de Busca */}
                            <div className="relative">
                                <Search className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    placeholder="Buscar por modelo, marca, ano..."
                                    className="pl-9"
                                    value={search}
                                    onChange={(e) => setSearch(e.target.value)}
                                    onKeyPress={(e) =>
                                        e.key === 'Enter' && handleFilter()
                                    }
                                />
                            </div>

                            {/* Filtros Avançados */}
                            <div>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="text-sm text-muted-foreground"
                                    onClick={() => setShowFilters(!showFilters)}
                                >
                                    <Filter className="mr-2 h-4 w-4" />
                                    {showFilters
                                        ? 'Menos filtros'
                                        : 'Mais filtros'}
                                </Button>

                                {showFilters && (
                                    <div className="mt-4 grid grid-cols-1 gap-4 border-t pt-4 md:grid-cols-2 lg:grid-cols-4">
                                        <div className="space-y-2">
                                            <label className="text-sm font-medium">
                                                Marca
                                            </label>
                                            <Select
                                                value={selectedBrand}
                                                onValueChange={setSelectedBrand}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Todas as marcas" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {brands.map((brand) => (
                                                        <SelectItem
                                                            key={brand.id}
                                                            value={brand.id.toString()}
                                                        >
                                                            {brand.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div className="space-y-2">
                                            <label className="text-sm font-medium">
                                                Categoria
                                            </label>
                                            <Select
                                                value={selectedCategory}
                                                onValueChange={
                                                    setSelectedCategory
                                                }
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Todas as categorias" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {categories.map(
                                                        (category) => (
                                                            <SelectItem
                                                                key={
                                                                    category.id
                                                                }
                                                                value={category.id.toString()}
                                                            >
                                                                {category.name}
                                                            </SelectItem>
                                                        ),
                                                    )}
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div className="space-y-2">
                                            <label className="text-sm font-medium">
                                                Preço mínimo
                                            </label>
                                            <div className="relative">
                                                <span className="absolute top-2.5 left-3 text-sm text-muted-foreground">
                                                    R$
                                                </span>
                                                <Input
                                                    className="pl-8"
                                                    placeholder="Mínimo"
                                                    value={minPrice}
                                                    onChange={(e) =>
                                                        setMinPrice(
                                                            e.target.value.replace(
                                                                /\D/g,
                                                                '',
                                                            ),
                                                        )
                                                    }
                                                />
                                            </div>
                                        </div>

                                        <div className="space-y-2">
                                            <label className="text-sm font-medium">
                                                Preço máximo
                                            </label>
                                            <div className="relative">
                                                <span className="absolute top-2.5 left-3 text-sm text-muted-foreground">
                                                    R$
                                                </span>
                                                <Input
                                                    className="pl-8"
                                                    placeholder="Máximo"
                                                    value={maxPrice}
                                                    onChange={(e) =>
                                                        setMaxPrice(
                                                            e.target.value.replace(
                                                                /\D/g,
                                                                '',
                                                            ),
                                                        )
                                                    }
                                                />
                                            </div>
                                        </div>

                                        <div className="space-y-2">
                                            <label className="text-sm font-medium">
                                                Combustível
                                            </label>
                                            <Select
                                                value={selectedFuelType}
                                                onValueChange={
                                                    setSelectedFuelType
                                                }
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Todos os tipos" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {Object.entries(
                                                        fuelTypes,
                                                    ).map(([value, label]) => (
                                                        <SelectItem
                                                            key={value}
                                                            value={value}
                                                        >
                                                            {label}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div className="space-y-2">
                                            <label className="text-sm font-medium">
                                                Câmbio
                                            </label>
                                            <Select
                                                value={selectedTransmission}
                                                onValueChange={
                                                    setSelectedTransmission
                                                }
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Todos os tipos" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {Object.entries(
                                                        transmissions,
                                                    ).map(([value, label]) => (
                                                        <SelectItem
                                                            key={value}
                                                            value={value}
                                                        >
                                                            {label}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div className="flex items-end gap-2">
                                            <Button
                                                className="flex-1"
                                                onClick={handleFilter}
                                            >
                                                Aplicar Filtros
                                            </Button>
                                            <Button
                                                variant="outline"
                                                onClick={resetFilters}
                                            >
                                                Limpar
                                            </Button>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Lista de Veículos */}
            <div className="container py-12">
                <div className="mb-6 flex items-center justify-between">
                    <h2 className="text-2xl font-bold">
                        {vehicles.total} veículos encontrados
                    </h2>
                    <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">
                            Ordenar por:
                        </span>
                        <Select defaultValue="recent">
                            <SelectTrigger className="w-[180px]">
                                <SelectValue placeholder="Mais recentes" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="recent">
                                    Mais recentes
                                </SelectItem>
                                <SelectItem value="price_asc">
                                    Menor preço
                                </SelectItem>
                                <SelectItem value="price_desc">
                                    Maior preço
                                </SelectItem>
                                <SelectItem value="year_desc">
                                    Mais novos
                                </SelectItem>
                                <SelectItem value="mileage_asc">
                                    Menor quilometragem
                                </SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                {vehicles.data.length === 0 ? (
                    <div className="py-12 text-center">
                        <h3 className="mb-2 text-lg font-medium">
                            Nenhum veículo encontrado
                        </h3>
                        <p className="text-muted-foreground">
                            Tente ajustar seus filtros para encontrar o que
                            procura.
                        </p>
                        <Button
                            variant="outline"
                            className="mt-4"
                            onClick={resetFilters}
                        >
                            Limpar todos os filtros
                        </Button>
                    </div>
                ) : (
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                        {vehicles.data.map((vehicle) => {
                            const mainImage = vehicle.images?.[0];
                            return (
                                <Link
                                    key={vehicle.id}
                                    href={route('vehicles.show', {
                                        vehicle: vehicle.slug,
                                    })}
                                    className="group"
                                >
                                    <Card className="h-full overflow-hidden transition-all duration-300 hover:shadow-lg">
                                        <div className="relative aspect-video bg-muted">
                                            {mainImage ? (
                                                <img
                                                    src={mainImage.url}
                                                    alt={`${vehicle.brand.name} ${vehicle.model}`}
                                                    className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
                                                />
                                            ) : (
                                                <div className="flex h-full w-full items-center justify-center bg-muted">
                                                    <span className="text-muted-foreground">
                                                        Sem imagem
                                                    </span>
                                                </div>
                                            )}
                                            {vehicle.is_featured && (
                                                <div className="absolute top-3 left-3">
                                                    <div className="rounded bg-yellow-500 px-2 py-1 text-xs font-medium text-white">
                                                        Destaque
                                                    </div>
                                                </div>
                                            )}
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                className="absolute top-2 right-2 h-8 w-8 rounded-full bg-background/80 backdrop-blur-sm hover:bg-background"
                                                onClick={(e) => {
                                                    e.preventDefault();
                                                    e.stopPropagation();
                                                    // Adicionar aos favoritos
                                                }}
                                            >
                                                <Heart className="h-4 w-4" />
                                                <span className="sr-only">
                                                    Adicionar aos favoritos
                                                </span>
                                            </Button>
                                        </div>
                                        <div className="p-4">
                                            <div className="mb-2 flex items-start justify-between">
                                                <div>
                                                    <h3 className="font-semibold transition-colors group-hover:text-primary">
                                                        {vehicle.brand.name}{' '}
                                                        {vehicle.model}
                                                    </h3>
                                                    <div className="text-sm text-muted-foreground">
                                                        {vehicle.mileage
                                                            ? parseInt(
                                                                  vehicle.mileage.toString(),
                                                              ).toLocaleString(
                                                                  'pt-BR',
                                                              )
                                                            : '0'}{' '}
                                                        km
                                                    </div>
                                                </div>
                                                <div className="flex items-center rounded bg-muted px-2 py-0.5 text-sm">
                                                    <Star className="mr-1 h-3.5 w-3.5 fill-yellow-500 text-yellow-500" />
                                                    <span>4.8</span>
                                                </div>
                                            </div>

                                            <div className="mt-4 flex items-center justify-between">
                                                <div>
                                                    {vehicle.promotional_price ? (
                                                        <div>
                                                            <span className="text-sm text-muted-foreground line-through">
                                                                {formatCurrency(
                                                                    vehicle.price,
                                                                )}
                                                            </span>
                                                            <div className="text-lg font-bold text-red-600">
                                                                {formatCurrency(
                                                                    vehicle.promotional_price,
                                                                )}
                                                            </div>
                                                        </div>
                                                    ) : (
                                                        <div className="text-lg font-bold">
                                                            {formatCurrency(
                                                                vehicle.price,
                                                            )}
                                                        </div>
                                                    )}
                                                    {vehicle.is_negotiable && (
                                                        <div className="text-xs text-green-600">
                                                            Aceita troca
                                                        </div>
                                                    )}
                                                </div>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    className="text-primary"
                                                >
                                                    Ver detalhes
                                                    <ArrowRight className="ml-1 h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>
                                    </Card>
                                </Link>
                            );
                        })}
                    </div>
                )}

                {/* Paginação */}
                {vehicles.last_page > 1 && (
                    <div className="mt-10 flex justify-center">
                        <div className="flex items-center gap-1">
                            <Button
                                variant="outline"
                                size="sm"
                                disabled={vehicles.current_page === 1}
                                onClick={() =>
                                    vehicles.prev_page_url &&
                                    router.get(vehicles.prev_page_url)
                                }
                            >
                                Anterior
                            </Button>

                            {Array.from(
                                { length: vehicles.last_page },
                                (_, i) => i + 1,
                            ).map((page) => (
                                <Button
                                    key={page}
                                    variant={
                                        vehicles.current_page === page
                                            ? 'default'
                                            : 'outline'
                                    }
                                    size="sm"
                                    className={
                                        vehicles.current_page === page
                                            ? 'font-bold'
                                            : ''
                                    }
                                    onClick={() =>
                                        router.get(
                                            vehicles.path + '?page=' + page,
                                            {},
                                            {
                                                preserveState: true,
                                                preserveScroll: true,
                                            },
                                        )
                                    }
                                >
                                    {page}
                                </Button>
                            ))}

                            <Button
                                variant="outline"
                                size="sm"
                                disabled={
                                    vehicles.current_page === vehicles.last_page
                                }
                                onClick={() =>
                                    vehicles.next_page_url &&
                                    router.get(vehicles.next_page_url)
                                }
                            >
                                Próximo
                            </Button>
                        </div>
                    </div>
                )}
            </div>

            {/* CTA Section */}
            <div className="bg-primary/5 py-16">
                <div className="container text-center">
                    <h2 className="mb-4 text-3xl font-bold">
                        Não encontrou o que procurava?
                    </h2>
                    <p className="mx-auto mb-8 max-w-2xl text-lg text-muted-foreground">
                        Nossa equipe pode te ajudar a encontrar o veículo
                        perfeito para você.
                    </p>
                    <div className="flex flex-col justify-center gap-4 sm:flex-row">
                        <Button size="lg" className="gap-2">
                            <Phone className="h-4 w-4" />
                            Fale com um consultor
                        </Button>
                        <Button variant="outline" size="lg" asChild>
                            <Link href="#">Receber ofertas por e-mail</Link>
                        </Button>
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
