import MainLayout from '@/layouts/MainLayout';
import { Head } from '@inertiajs/react';

export default function Security() {
    return (
        <MainLayout>
            <Head title="Dicas de Segurança" />
            
            <div className="container mx-auto px-4 py-8">
                <div className="max-w-4xl mx-auto">
                    <h1 className="text-3xl font-bold mb-8">Dicas de Segurança</h1>
                    
                    <div className="space-y-8">
                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                            <h2 className="text-xl font-semibold mb-4 text-yellow-800">⚠️ Importante</h2>
                            <p className="text-yellow-700">
                                Sua segurança é nossa prioridade. Siga sempre estas dicas para uma experiência segura na plataforma.
                            </p>
                        </div>
                        
                        <div className="grid gap-6 md:grid-cols-2">
                            <div className="bg-white rounded-lg shadow-md p-6">
                                <h3 className="text-lg font-semibold mb-4">🛡️ Para Compradores</h3>
                                <ul className="space-y-2 text-gray-600">
                                    <li>• Sempre veja o veículo pessoalmente</li>
                                    <li>• Verifique a documentação</li>
                                    <li>• Faça um test drive</li>
                                    <li>• Negocie em locais públicos</li>
                                    <li>• Desconfie de preços muito baixos</li>
                                    <li>• Use formas de pagamento seguras</li>
                                </ul>
                            </div>
                            
                            <div className="bg-white rounded-lg shadow-md p-6">
                                <h3 className="text-lg font-semibold mb-4">🔒 Para Vendedores</h3>
                                <ul className="space-y-2 text-gray-600">
                                    <li>• Mantenha seus dados atualizados</li>
                                    <li>• Use fotos reais do veículo</li>
                                    <li>• Seja transparente na descrição</li>
                                    <li>• Encontre compradores em locais seguros</li>
                                    <li>• Confirme o pagamento antes da entrega</li>
                                    <li>• Guarde comprovantes da venda</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div className="bg-white rounded-lg shadow-md p-6">
                            <h3 className="text-lg font-semibold mb-4">🚨 Sinais de Alerta</h3>
                            <div className="grid gap-4 md:grid-cols-2">
                                <div>
                                    <h4 className="font-medium mb-2">Evite negócios que:</h4>
                                    <ul className="space-y-1 text-gray-600 text-sm">
                                        <li>• Pedem pagamento antecipado</li>
                                        <li>• Oferecem preços irreais</li>
                                        <li>• Pressionam para decisão rápida</li>
                                        <li>• Não permitem inspeção</li>
                                        <li>• Usam apenas comunicação online</li>
                                    </ul>
                                </div>
                                <div>
                                    <h4 className="font-medium mb-2">Documentos obrigatórios:</h4>
                                    <ul className="space-y-1 text-gray-600 text-sm">
                                        <li>• CRLV (Certificado de Registro)</li>
                                        <li>• Nota fiscal ou DUT</li>
                                        <li>• Comprovante de quitação</li>
                                        <li>• Laudo de vistoria (se aplicável)</li>
                                        <li>• Identidade do proprietário</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                            <h3 className="text-lg font-semibold mb-4 text-blue-800">💡 Dica Extra</h3>
                            <p className="text-blue-700">
                                Em caso de dúvidas ou problemas, entre em contato conosco imediatamente. 
                                Nossa equipe está sempre pronta para ajudar e garantir sua segurança.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
