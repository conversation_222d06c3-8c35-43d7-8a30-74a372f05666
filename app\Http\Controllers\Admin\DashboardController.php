<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\Part;
use App\Models\Advertisement;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     */
    public function index()
    {
        // Estatísticas gerais
        $stats = [
            'total_users' => User::count(),
            'total_vehicles' => Vehicle::count(),
            'total_parts' => Part::count(),
            'total_advertisements' => Advertisement::count(),
            'active_vehicles' => Vehicle::where('status', 'published')->count(),
            'pending_vehicles' => Vehicle::where('status', 'pending')->count(),
            'users_this_month' => User::whereMonth('created_at', Carbon::now()->month)->count(),
            'vehicles_this_month' => Vehicle::whereMonth('created_at', Carbon::now()->month)->count(),
        ];

        // Veículos recentes
        $recentVehicles = Vehicle::with(['brand', 'category', 'user'])
            ->latest()
            ->take(5)
            ->get();

        // Usuários recentes
        $recentUsers = User::latest()
            ->take(5)
            ->get(['id', 'name', 'email', 'created_at', 'status']);

        // Dados para gráficos
        $chartData = $this->getChartData();

        return Inertia::render('Admin/Dashboard', [
            'stats' => $stats,
            'recentVehicles' => $recentVehicles,
            'recentUsers' => $recentUsers,
            'chartData' => $chartData,
        ]);
    }

    /**
     * Get chart data for dashboard.
     */
    private function getChartData()
    {
        // Dados dos últimos 7 dias
        $days = collect();
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $days->push([
                'date' => $date->format('Y-m-d'),
                'label' => $date->format('d/m'),
                'users' => User::whereDate('created_at', $date)->count(),
                'vehicles' => Vehicle::whereDate('created_at', $date)->count(),
                'parts' => Part::whereDate('created_at', $date)->count(),
            ]);
        }

        // Status dos veículos
        $vehicleStatus = [
            'published' => Vehicle::where('status', 'published')->count(),
            'draft' => Vehicle::where('status', 'draft')->count(),
            'pending' => Vehicle::where('status', 'pending')->count(),
            'inactive' => Vehicle::where('status', 'inactive')->count(),
        ];

        // Categorias mais populares
        $popularCategories = Vehicle::join('categories', 'vehicles.category_id', '=', 'categories.id')
            ->selectRaw('categories.name, COUNT(*) as count')
            ->groupBy('categories.id', 'categories.name')
            ->orderByDesc('count')
            ->take(5)
            ->get();

        return [
            'daily' => $days,
            'vehicleStatus' => $vehicleStatus,
            'popularCategories' => $popularCategories,
        ];
    }
}
