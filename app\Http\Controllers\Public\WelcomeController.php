<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Vehicle;
use Inertia\Inertia;

class WelcomeController extends Controller
{
    /**
     * Exibe a página inicial.
     */
    public function index()
    {
        // Carrega as categorias ativas
        $categories = Category::where('is_active', true)
            ->whereNull('parent_id')
            ->with(['children' => function($query) {
                $query->where('is_active', true);
            }])
            ->orderBy('order')
            ->get(['id', 'name', 'slug', 'icon', 'description'])
            ->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'description' => $category->description ?? 'Categoria de ' . $category->name,
                    'icon' => $category->icon,
                    'url' => '/categorias/' . $category->slug,
                    'vehicles_count' => rand(10, 100), // Placeholder - você pode implementar contagem real
                    'parts_count' => rand(5, 50), // Placeholder - você pode implementar contagem real
                ];
            });

        // Busca os veículos em destaque
        $featuredListings = Vehicle::with(['mainImage', 'category', 'brand'])
            ->where('is_featured', true)
            ->where('status', 'published')
            ->latest()
            ->take(8)
            ->get()
            ->map(function ($vehicle) {
                return [
                    'id' => $vehicle->id,
                    'title' => $vehicle->title,
                    'model' => $vehicle->model,
                    'price' => (float) $vehicle->price,
                    'location' => $vehicle->city . ' - ' . $vehicle->state,
                    'created_at' => $vehicle->created_at->toDateTimeString(),
                    'main_image' => $vehicle->mainImage ? [
                        'url' => $vehicle->mainImage->getImageUrl()
                    ] : null,
                    'category' => [
                        'name' => $vehicle->category->name ?? 'Sem categoria',
                    ],
                    'brand' => [
                        'name' => $vehicle->brand->name ?? 'Sem marca',
                    ],
                    'is_featured' => $vehicle->is_featured,
                    'year_manufacture' => $vehicle->year_manufacture,
                    'mileage' => $vehicle->mileage,
                    'fuel_type' => $vehicle->fuel_type,
                    'transmission' => $vehicle->transmission,
                ];
            });

        return Inertia::render('welcome', [
            'categories' => $categories,
            'featuredVehicles' => $featuredListings,
            'featuredParts' => [],
        ]);
    }
}
