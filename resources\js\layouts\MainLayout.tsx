import Footer from '@/components/Footer';
import HeaderOLX from '@/components/HeaderOLX';
import { Category } from '@/types';
import { ReactNode } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
}

interface MainLayoutProps {
    categories?: Category[];
    children: ReactNode;
    auth?: {
        user: User | null;
    };
    errors?: Record<string, string>;
    title?: string;
}

export default function MainLayout({
    categories = [],
    children,
    title = 'VeiculosBR - Loja de Veículos',
}: MainLayoutProps) {
    return (
        <div className="min-h-screen bg-background">
            <HeaderOLX categories={categories} />
            <main>
                {title && (
                    <div className="container mx-auto px-4 py-8">
                        <h1 className="mb-8 text-3xl font-bold">{title}</h1>
                    </div>
                )}
                {children}
            </main>
            <Footer />
        </div>
    );
}
